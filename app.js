var express = require('express');
var path = require('path');
var beconnect_api = require('./routes/beconnect_api.js');
var db_exec = require('./routes/db_exec.js');
var tencent = require('./routes/tencent.js');
var trans = require('./routes/trans.js');
var onlinete = require('./routes/onlinete.js');
var accesschecker = require('./routes/access-checker.js');
var sms = require('./routes/sms.js')
var sendmail = require('./routes/sendmail.js')
var calendar = require('./routes/calendar')
var jwt = require('./routes/jwt');
var app = express();
var fs = require('fs');
var https = require('https');
//var bodyParser = require('body-parser');

//allow orgin
app.all('*', function (req, res, next) {
	res.header("Access-Control-Allow-Origin", "*");
	res.header('Access-Control-Allow-Methods', 'PUT, GET, POST, DELETE, OPTIONS');
	res.header("Access-Control-Allow-Headers", "*");
	next();
});

// var options = {
// 	key: fs.readFileSync('./connect.be.co.key'),
// 	cert: fs.readFileSync('./connect.be.co.pem')
// };
// var serverPort = 4000;

// var server = https.createServer(options, app);

// server.listen(serverPort, function () {
//  	console.log('server up and running at %s port', serverPort);
// });

//app.use(bodyParser.urlencoded({ extended: false }))
app.use(express.static(path.join(__dirname, 'public')));

app.use('/beconnect_api', beconnect_api);
app.use('/db_exec', db_exec);
app.use('/tencent', tencent);
app.use('/trans', trans);
app.use('/onlinete', onlinete);
app.use('/access-checker', accesschecker);
app.use('/sms', sms)
app.use('/sendmail', sendmail)
app.use('/calendar', calendar)
app.use('/jwt', jwt);

//swagger
var swaggerJsDoc = require('swagger-jsdoc');
var swaggerUi = require('swagger-ui-express');
const swaggerOptions = {
	swaggerDefinition: {
		info: {
			title: 'BE Connect - TE System API',
			description: 'API information',
			contact: {
				name: 'BE IT'
			},
			servers: ['http://localhost:3030']
		}
	},
	apis: ['./routes/*.js']
};
const swaggerDocs = swaggerJsDoc(swaggerOptions);
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs));


// catch 404 and forward to error handler
app.use(function (req, res, next) {
	var err = new Error('Not Found');
	err.status = 404;
	next(err);
});

// error handler
app.use(function (err, req, res, next) {
	// set locals, only providing error in development
	res.locals.message = err.message;
	res.locals.error = req.app.get('env') === 'development' ? err : {};

	// render the error page
	res.status(err.status || 500);
	res.render('error');
});


module.exports = app;
