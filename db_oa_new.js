/**
 * MySQL数据库帮助类 - OA系统专用
 * 提供完整的数据库操作功能，包括连接池管理、事务支持、CRUD操作等
 * @author: 系统开发团队
 * @version: 2.0.0
 */

const mysql = require('mysql');
const util = require('util');

class DatabaseHelper {
    constructor() {
        this.pool = null;
        this.env = process.env.NODE_ENV || 'prod';
        this.isConnected = false;
        this.init();
    }

    /**
     * 初始化数据库连接池
     */
    init() {
        try {
            const config = this.getConfig();
            this.pool = mysql.createPool(config);

            // 将pool.query转换为Promise
            this.pool.query = util.promisify(this.pool.query).bind(this.pool);

            // 测试连接
            this.testConnection();

            console.log(`[DB] MySQL连接池已初始化 - 环境: ${this.env}, 数据库: ${config.database}`);
        } catch (error) {
            console.error('[DB] 数据库初始化失败:', error);
            throw error;
        }
    }

    /**
     * 获取数据库配置
     * @returns {Object} 数据库配置对象
     */
    getConfig() {
        const configs = {
            prod: {
                connectionLimit: 20,
                acquireTimeout: 60000,
                timeout: 60000,
                reconnect: true,
                host: 'rm-j6c95u8ak3n7w3cc9wo.mysql.rds.aliyuncs.com',
                user: 'root',
                password: '5RGbetad^&CVDAZ',
                database: 'eteams',
                charset: 'utf8mb4',
                timezone: '+08:00'
            },
            dev: {
                connectionLimit: 10,
                acquireTimeout: 60000,
                timeout: 60000,
                reconnect: true,
                host: '127.0.0.1',
                user: 'root',
                password: 'password',
                database: 'eteams_dev',
                charset: 'utf8mb4',
                timezone: '+08:00'
            },
            test: {
                connectionLimit: 5,
                acquireTimeout: 60000,
                timeout: 60000,
                reconnect: true,
                host: '127.0.0.1',
                user: 'root',
                password: 'password',
                database: 'eteams_test',
                charset: 'utf8mb4',
                timezone: '+08:00'
            }
        };

        return configs[this.env] || configs.prod;
    }

    /**
     * 测试数据库连接
     */
    async testConnection() {
        try {
            const connection = await this.getConnection();
            await this.query('SELECT 1 as test');
            this.releaseConnection(connection);
            this.isConnected = true;
            console.log('[DB] 数据库连接测试成功');
        } catch (error) {
            this.isConnected = false;
            console.error('[DB] 数据库连接测试失败:', error);
            throw error;
        }
    }

    /**
     * 获取数据库连接
     * @returns {Promise<Connection>} 数据库连接对象
     */
    getConnection() {
        return new Promise((resolve, reject) => {
            this.pool.getConnection((err, connection) => {
                if (err) {
                    console.error('[DB] 获取连接失败:', err);
                    reject(err);
                } else {
                    resolve(connection);
                }
            });
        });
    }

    /**
     * 释放数据库连接
     * @param {Connection} connection 数据库连接对象
     */
    releaseConnection(connection) {
        if (connection) {
            connection.release();
        }
    }

    /**
     * 执行SQL查询
     * @param {string} sql SQL语句
     * @param {Array} params 参数数组
     * @returns {Promise<Array>} 查询结果
     */
    async query(sql, params = []) {
        if (!sql || typeof sql !== 'string') {
            throw new Error('SQL语句不能为空且必须是字符串');
        }

        const startTime = Date.now();
        let connection = null;

        try {
            connection = await this.getConnection();

            const result = await new Promise((resolve, reject) => {
                connection.query(sql, params, (err, rows, fields) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve({ rows, fields });
                    }
                });
            });

            const duration = Date.now() - startTime;
            console.log(`[DB] 查询执行成功 - 耗时: ${duration}ms, 结果数: ${result.rows.length}`);

            return result.rows;
        } catch (error) {
            const duration = Date.now() - startTime;
            console.error(`[DB] 查询执行失败 - 耗时: ${duration}ms, SQL: ${sql}, 错误:`, error);
            throw error;
        } finally {
            this.releaseConnection(connection);
        }
    }

    /**
     * 执行单条查询（返回第一条记录）
     * @param {string} sql SQL语句
     * @param {Array} params 参数数组
     * @returns {Promise<Object|null>} 查询结果
     */
    async queryOne(sql, params = []) {
        const results = await this.query(sql, params);
        return results.length > 0 ? results[0] : null;
    }

    /**
     * 插入数据
     * @param {string} table 表名
     * @param {Object} data 要插入的数据对象
     * @returns {Promise<Object>} 插入结果，包含insertId
     */
    async insert(table, data) {
        if (!table || !data || typeof data !== 'object') {
            throw new Error('表名和数据对象不能为空');
        }

        const keys = Object.keys(data);
        const values = Object.values(data);
        const placeholders = keys.map(() => '?').join(', ');

        const sql = `INSERT INTO ${table} (${keys.join(', ')}) VALUES (${placeholders})`;

        try {
            const result = await this.query(sql, values);
            return {
                insertId: result.insertId,
                affectedRows: result.affectedRows
            };
        } catch (error) {
            console.error(`[DB] 插入数据失败 - 表: ${table}`, error);
            throw error;
        }
    }

    /**
     * 批量插入数据
     * @param {string} table 表名
     * @param {Array} dataArray 要插入的数据数组
     * @returns {Promise<Object>} 插入结果
     */
    async insertBatch(table, dataArray) {
        if (!table || !Array.isArray(dataArray) || dataArray.length === 0) {
            throw new Error('表名和数据数组不能为空');
        }

        const keys = Object.keys(dataArray[0]);
        const placeholders = keys.map(() => '?').join(', ');
        const valuesSql = dataArray.map(() => `(${placeholders})`).join(', ');

        const sql = `INSERT INTO ${table} (${keys.join(', ')}) VALUES ${valuesSql}`;
        const values = dataArray.flatMap(item => Object.values(item));

        try {
            const result = await this.query(sql, values);
            return {
                insertId: result.insertId,
                affectedRows: result.affectedRows
            };
        } catch (error) {
            console.error(`[DB] 批量插入数据失败 - 表: ${table}`, error);
            throw error;
        }
    }

    /**
     * 更新数据
     * @param {string} table 表名
     * @param {Object} data 要更新的数据对象
     * @param {Object} where 更新条件
     * @returns {Promise<Object>} 更新结果
     */
    async update(table, data, where) {
        if (!table || !data || !where || typeof data !== 'object' || typeof where !== 'object') {
            throw new Error('表名、数据对象和条件对象不能为空');
        }

        const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');
        const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');

        const sql = `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`;
        const values = [...Object.values(data), ...Object.values(where)];

        try {
            const result = await this.query(sql, values);
            return {
                affectedRows: result.affectedRows,
                changedRows: result.changedRows
            };
        } catch (error) {
            console.error(`[DB] 更新数据失败 - 表: ${table}`, error);
            throw error;
        }
    }

    /**
     * 删除数据
     * @param {string} table 表名
     * @param {Object} where 删除条件
     * @returns {Promise<Object>} 删除结果
     */
    async delete(table, where) {
        if (!table || !where || typeof where !== 'object') {
            throw new Error('表名和条件对象不能为空');
        }

        const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
        const sql = `DELETE FROM ${table} WHERE ${whereClause}`;
        const values = Object.values(where);

        try {
            const result = await this.query(sql, values);
            return {
                affectedRows: result.affectedRows
            };
        } catch (error) {
            console.error(`[DB] 删除数据失败 - 表: ${table}`, error);
            throw error;
        }
    }

    /**
     * 查询数据（带分页）
     * @param {string} table 表名
     * @param {Object} options 查询选项
     * @returns {Promise<Object>} 查询结果，包含数据和分页信息
     */
    async select(table, options = {}) {
        if (!table) {
            throw new Error('表名不能为空');
        }

        const {
            fields = '*',
            where = {},
            orderBy = '',
            limit = 0,
            offset = 0
        } = options;

        let sql = `SELECT ${fields} FROM ${table}`;
        const values = [];

        // 添加WHERE条件
        if (Object.keys(where).length > 0) {
            const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
            sql += ` WHERE ${whereClause}`;
            values.push(...Object.values(where));
        }

        // 添加ORDER BY
        if (orderBy) {
            sql += ` ORDER BY ${orderBy}`;
        }

        // 添加LIMIT和OFFSET
        if (limit > 0) {
            sql += ` LIMIT ${limit}`;
            if (offset > 0) {
                sql += ` OFFSET ${offset}`;
            }
        }

        try {
            const data = await this.query(sql, values);

            // 如果需要分页信息，查询总数
            let total = data.length;
            if (limit > 0) {
                let countSql = `SELECT COUNT(*) as total FROM ${table}`;
                if (Object.keys(where).length > 0) {
                    const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
                    countSql += ` WHERE ${whereClause}`;
                }
                const countResult = await this.query(countSql, Object.values(where));
                total = countResult[0].total;
            }

            return {
                data,
                total,
                limit,
                offset
            };
        } catch (error) {
            console.error(`[DB] 查询数据失败 - 表: ${table}`, error);
            throw error;
        }
    }

    /**
     * 执行事务
     * @param {Function} callback 事务回调函数，接收connection参数
     * @returns {Promise<any>} 事务执行结果
     */
    async transaction(callback) {
        if (typeof callback !== 'function') {
            throw new Error('事务回调必须是一个函数');
        }

        let connection = null;

        try {
            connection = await this.getConnection();

            // 开始事务
            await new Promise((resolve, reject) => {
                connection.beginTransaction(err => {
                    if (err) reject(err);
                    else resolve();
                });
            });

            console.log('[DB] 事务开始');

            // 创建事务专用的查询方法
            const transactionQuery = (sql, params = []) => {
                return new Promise((resolve, reject) => {
                    connection.query(sql, params, (err, results) => {
                        if (err) {
                            reject(err);
                        } else {
                            resolve(results);
                        }
                    });
                });
            };

            // 执行事务回调
            const result = await callback(transactionQuery);

            // 提交事务
            await new Promise((resolve, reject) => {
                connection.commit(err => {
                    if (err) reject(err);
                    else resolve();
                });
            });

            console.log('[DB] 事务提交成功');
            return result;

        } catch (error) {
            console.error('[DB] 事务执行失败，正在回滚:', error);

            // 回滚事务
            if (connection) {
                await new Promise((resolve) => {
                    connection.rollback(() => {
                        console.log('[DB] 事务回滚完成');
                        resolve();
                    });
                });
            }

            throw error;
        } finally {
            this.releaseConnection(connection);
        }
    }

    /**
     * 检查表是否存在
     * @param {string} tableName 表名
     * @returns {Promise<boolean>} 表是否存在
     */
    async tableExists(tableName) {
        if (!tableName) {
            throw new Error('表名不能为空');
        }

        try {
            const sql = `
                SELECT COUNT(*) as count
                FROM information_schema.tables
                WHERE table_schema = DATABASE() AND table_name = ?
            `;
            const result = await this.query(sql, [tableName]);
            return result[0].count > 0;
        } catch (error) {
            console.error(`[DB] 检查表存在性失败 - 表: ${tableName}`, error);
            throw error;
        }
    }

    /**
     * 获取表结构信息
     * @param {string} tableName 表名
     * @returns {Promise<Array>} 表结构信息
     */
    async getTableStructure(tableName) {
        if (!tableName) {
            throw new Error('表名不能为空');
        }

        try {
            const sql = `DESCRIBE ${tableName}`;
            return await this.query(sql);
        } catch (error) {
            console.error(`[DB] 获取表结构失败 - 表: ${tableName}`, error);
            throw error;
        }
    }

    /**
     * 获取连接池状态
     * @returns {Object} 连接池状态信息
     */
    getPoolStatus() {
        if (!this.pool) {
            return { error: '连接池未初始化' };
        }

        try {
            return {
                totalConnections: this.pool._allConnections ? this.pool._allConnections.length : 0,
                freeConnections: this.pool._freeConnections ? this.pool._freeConnections.length : 0,
                connectionLimit: this.pool.config.connectionLimit,
                isConnected: this.isConnected,
                environment: this.env
            };
        } catch (error) {
            return {
                error: '无法获取连接池详细状态',
                connectionLimit: this.pool.config.connectionLimit,
                isConnected: this.isConnected,
                environment: this.env
            };
        }
    }

    /**
     * 执行原生SQL（支持多语句）
     * @param {string} sql SQL语句
     * @param {Array} params 参数数组
     * @returns {Promise<Array>} 执行结果
     */
    async executeRaw(sql, params = []) {
        if (!sql) {
            throw new Error('SQL语句不能为空');
        }

        console.log('[DB] 执行原生SQL:', sql);
        return await this.query(sql, params);
    }

    /**
     * 关闭连接池
     * @returns {Promise<void>}
     */
    async close() {
        if (this.pool) {
            return new Promise((resolve) => {
                this.pool.end(() => {
                    console.log('[DB] 连接池已关闭');
                    this.isConnected = false;
                    resolve();
                });
            });
        }
    }

    /**
     * 重新连接数据库
     * @returns {Promise<void>}
     */
    async reconnect() {
        console.log('[DB] 正在重新连接数据库...');
        await this.close();
        this.init();
        await this.testConnection();
        console.log('[DB] 数据库重连成功');
    }

    /**
     * 获取数据库版本信息
     * @returns {Promise<string>} 数据库版本
     */
    async getVersion() {
        try {
            const result = await this.query('SELECT VERSION() as version');
            return result[0].version;
        } catch (error) {
            console.error('[DB] 获取数据库版本失败:', error);
            throw error;
        }
    }

    /**
     * 执行存储过程
     * @param {string} procedureName 存储过程名称
     * @param {Array} params 参数数组
     * @returns {Promise<Array>} 执行结果
     */
    async callProcedure(procedureName, params = []) {
        if (!procedureName) {
            throw new Error('存储过程名称不能为空');
        }

        const placeholders = params.map(() => '?').join(', ');
        const sql = `CALL ${procedureName}(${placeholders})`;

        try {
            return await this.query(sql, params);
        } catch (error) {
            console.error(`[DB] 执行存储过程失败 - 过程: ${procedureName}`, error);
            throw error;
        }
    }
}

// 创建单例实例
const dbInstance = new DatabaseHelper();

// 导出实例和类
module.exports = dbInstance;
module.exports.DatabaseHelper = DatabaseHelper;